"use server";

import { HttpTypes } from "@medusajs/types";
import { sdk } from "@/libs/medusaClient";

export interface ReviewStats {
  average_rating: number;
  rating_distribution: { [key: string]: number };
  review_count: number;
}

export interface ProductReviewsParams extends HttpTypes.FindParams {
  product_id?: string;
  rating?: number;
  status?: string;
}

export const getProductReviewStats = async (
  productId: string
): Promise<ReviewStats> => {
  try {

    const response = await sdk.client.fetch<ReviewStats>(
      `/store/product-review-stats/?product_id=${productId}`,
      {
        method: "GET",
        cache: "force-cache",
      }
    );

    return {
      average_rating: response.data.data[0].average_rating,
      review_count: response.data.data[0].review_count,
    };

  } catch (error) {
    console.error("Error fetching product review stats:", error);
    return {
      average_rating: 0,
      rating_distribution: {},
      review_count: 0,
    };
  }
};

export interface ReviewsApiResponse {
  data: {
    data: any[];
    count: number;
    offset: number;
    limit: number;
  };
  message: string;
  status: number;
  success: boolean;
}

export const listProductReviews = async ({
  limit = 12,
  offset = 0, // eslint-disable-line @typescript-eslint/no-unused-vars
  queryParams, // eslint-disable-line @typescript-eslint/no-unused-vars
}: {
  limit?: number;
  offset?: number;
  queryParams?: ProductReviewsParams;
}): Promise<ReviewsApiResponse> => {
  try {
    const response = await sdk.client.fetch<ReviewsApiResponse>(
      `/store/product-reviews`,
      {
        headers: {
          "Content-Type": "application/json",
        },
        method: "GET",

        cache: "force-cache",
      }
    );
    console.log(`hey`, response.data.data);

    return response;
  } catch (error) {
    console.error("Error fetching product reviews:", error);
    return {
      data: {
        data: [],
        count: 0,
        offset: 0,
        limit,
      },
      message: "Error fetching reviews",
      status: 500,
      success: false,
    };
  }
};
